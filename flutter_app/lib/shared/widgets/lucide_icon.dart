import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// Lucide图标组件 - 与React UI完全一致的SVG图标
class LucideIcon extends StatelessWidget {
  final String iconName;
  final double? size;
  final Color? color;
  final double? strokeWidth;

  const LucideIcon({
    Key? key,
    required this.iconName,
    this.size = 24,
    this.color,
    this.strokeWidth,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      'assets/icons/$iconName.svg',
      width: size,
      height: size,
      colorFilter: color != null 
        ? ColorFilter.mode(color!, BlendMode.srcIn)
        : null,
      semanticsLabel: iconName,
    );
  }
}

/// 预定义的Lucide图标常量，方便使用
class LucideIcons {
  static const String home = 'home';
  static const String dumbbell = 'dumbbell';
  static const String calendar = 'calendar';
  static const String trophy = 'trophy';
  static const String user = 'user';
  static const String play = 'play';
  static const String zap = 'zap';
  static const String trendingUp = 'trending-up';
  static const String clock = 'clock';
  static const String flame = 'flame';
  static const String footprints = 'footprints';
}
